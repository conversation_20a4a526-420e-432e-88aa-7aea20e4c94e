# Speech-to-Text Transcription Tool

A modern, efficient audio transcription tool powered by OpenAI Whisper with UV package management.

## Features

- **Interactive CLI**: User-friendly menu system inspired by modern CLI tools
- **Latest Technology**: Uses OpenAI Whisper 20250625 and PyTorch 2.7.1
- **🇳🇴 Enhanced Norwegian Support**: Automatic NB-Whisper integration for superior Norwegian transcription
- **Modern Dependencies**: Librosa for audio processing, UV for package management
- **Multiple Formats**: Supports various audio/video formats
- **Batch Processing**: Process multiple files with YAML configuration
- **Model Management**: Download and manage Whisper models interactively
- **History Viewer**: Browse and view previous transcriptions
- **Multiple Languages**: English, Norwegian, French, Spanish, German
- **Flexible Models**: Choose from tiny to large Whisper models
- **Intelligent Model Selection**: Automatically uses best model for each language

## Requirements

- Python 3.9+ (optimized for Python 3.13)
- UV package manager
- FFmpeg (for audio processing)

## Quick Start

### 1. Install UV (if not already installed)

```powershell
# Windows PowerShell
irm https://astral.sh/uv/install.ps1 | iex
```

### 2. Navigate to Project Directory

```powershell
cd speech_to_text
```

### 3. Initialize Environment

```powershell
# Run the UV initialization script
.\uv_init.bat
```

### 4. Run the Application

```powershell
# Start interactive CLI (recommended)
.\run.bat

# Or run directly
uv run python src\main.py

# Command line mode
uv run python src\main.py audio1.mp3 audio2.wav --model medium

# Use configuration file
uv run python src\main.py --config batch_transcription_en.yaml --prompt
```

## Usage Modes

The application automatically determines which mode to use based on your input:

1. **Interactive CLI Mode**:
   - Triggered when: No arguments OR insufficient arguments provided
   - Example: `uv run python main.py` or `uv run python main.py --model large`

2. **Prompt Mode**:
   - Triggered when: `--prompt` flag is used
   - Behavior: Prompts for all settings using command-line args as defaults
   - Example: `uv run python main.py --model large --prompt`

3. **Direct Mode**:
   - Triggered when: Sufficient arguments provided without `--prompt`
   - Behavior: Executes immediately without prompts
   - Example: `uv run python main.py audio.mp3 --model large`

## Usage Examples

### Interactive Mode (Recommended)
```powershell
# Start the interactive CLI
uv run python main.py

# Follow the menu prompts:
# 1. Transcribe single audio file
# 2. Batch transcribe multiple files
# 3. Use configuration file
# 4. Download Whisper models
# 5. View transcription history
# 6. Settings & preferences
# 7. Exit
```

### Command Line Mode
```powershell
# Direct execution (no prompts)
uv run python main.py audioclip.mp3 --model large --language english

# Interactive prompts with command-line defaults
uv run python main.py audioclip.mp3 --model large --prompt
# This will prompt for all settings but use "audioclip.mp3" and "large" as defaults

# Batch processing with prompts
uv run python main.py --config batch_transcription_nor.yaml --prompt

# Insufficient arguments automatically trigger interactive CLI
uv run python main.py --model large
# This will start interactive CLI since no input files specified
```

## 🇳🇴 Enhanced Norwegian Transcription

This tool features **automatic NB-Whisper integration** for superior Norwegian speech recognition:

### What is NB-Whisper?
- **Developed by**: National Library of Norway (Nasjonalbiblioteket)
- **Training Data**: 66,000 hours of Norwegian speech
- **Languages**: Norwegian Bokmål (nb) and Norwegian Nynorsk (nn)
- **Performance**: Significantly better accuracy than standard Whisper for Norwegian

### Automatic Model Selection
When you set the language to `norwegian`, `no`, `nb`, or `nn`, the system automatically:
1. **Selects NB-Whisper** models for enhanced Norwegian accuracy
2. **Optimizes settings** with 28-second chunks and beam size 5
3. **Falls back gracefully** to standard Whisper if needed

### Available NB-Whisper Models
| Model | Parameters | Best For |
|-------|------------|----------|
| `tiny` | 39M | Fast processing, basic accuracy |
| `base` | 74M | Good balance of speed and quality |
| `small` | 244M | Better accuracy, moderate speed |
| `medium` | 769M | High accuracy, slower processing |
| `large` | 1550M | Best accuracy, requires more resources |

### Usage Examples
```powershell
# Automatic NB-Whisper selection for Norwegian
uv run python src\main.py norwegian_audio.mp3 --language norwegian --model large

# Explicit Norwegian Bokmål
uv run python src\main.py audio.wav --language nb --model medium

# Norwegian Nynorsk
uv run python src\main.py audio.mp3 --language nn --model base
```

### What to Expect During Processing
When using NB-Whisper for Norwegian transcription, you'll see:

1. **📦 Model Loading**: "Loading NB-Whisper model (this may take a moment for first use)"
2. **⏱️ Duration Estimate**: "Audio duration: X.X seconds"
3. **🔄 Progress Updates**: "Transcribing... (Xs elapsed)" every few seconds
4. **✅ Completion**: "NB-Whisper transcription completed in X.X seconds!"

**Note**: NB-Whisper processing may take longer than standard Whisper but provides significantly better Norwegian accuracy.

## Configuration

The tool supports YAML configuration files for batch processing:

- **`batch_transcription_example.yaml`** - Comprehensive example with all options and documentation
- `batch_transcription_en.yaml` - English batch processing template
- `batch_transcription_nor.yaml` - Norwegian batch processing template with NB-Whisper info

**Quick Start:** Copy `batch_transcription_example.yaml`, rename it, and modify the file paths for your audio files.

## Dependencies

All dependencies are managed through UV and specified in `pyproject.toml`:

- **openai-whisper**: Latest speech recognition
- **torch**: PyTorch for ML operations  
- **librosa**: Modern audio processing
- **rich**: Beautiful terminal output
- **loguru**: Enhanced logging

## Migration from pip to UV

This project has been migrated from pip to UV for better dependency management:

- ✅ Faster dependency resolution
- ✅ Better conflict resolution
- ✅ Reproducible builds
- ✅ Modern Python packaging standards
- ✅ Python 3.13 compatibility

## Project Structure

```
py__SpeechToText/
├── speech_to_text/                  # Main project directory
│   ├── src/                         # Source code
│   │   └── main.py                  # Main application
│   ├── pyproject.toml               # UV dependencies & project config
│   ├── uv.lock                      # Dependency lock file
│   ├── batch_transcription_example.yaml # Complete example with all options
│   ├── batch_transcription_en.yaml  # English batch config
│   ├── batch_transcription_nor.yaml # Norwegian batch config
│   ├── uv_init.bat                  # UV environment setup
│   ├── run.bat                      # Application runner
│   ├── README.md                    # Documentation
│   └── techstack.md                 # Technology overview
├── output/                          # Transcription outputs (created at runtime)
└── [other project files]            # Development files, logs, etc.
```

## Troubleshooting

### FFmpeg Not Found
Install FFmpeg and ensure it's in your PATH:
```powershell
# Using Chocolatey
choco install ffmpeg

# Using Scoop  
scoop install ffmpeg
```

### Python Version Issues
Ensure you're using Python 3.9+ (preferably 3.13):
```powershell
python --version
```

### UV Issues
Reinstall UV if needed:
```powershell
irm https://astral.sh/uv/install.ps1 | iex
```
