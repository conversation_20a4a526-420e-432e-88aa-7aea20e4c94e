# Technology Stack

## Core Framework
- **Python 3.9+** - Primary language (optimized for 3.13)
- **OpenAI Whisper 20250625** - Latest speech-to-text transcription engine
- **NB-Whisper** - Norwegian-optimized models from National Library of Norway
- **PyTorch 2.7.1** - Latest ML framework

## Key Dependencies
- **openai-whisper** - Audio transcription (latest stable)
- **transformers** - Hugging Face transformers (NB-Whisper integration)
- **librosa** - Modern audio processing (replaces pydub)
- **soundfile** - Audio I/O operations
- **numpy** - Numerical operations
- **rich** - Terminal UI/formatting
- **loguru** - Enhanced logging
- **tqdm** - Progress bars
- **PyYAML** - Configuration parsing
- **tiktoken** - OpenAI tokenization
- **torch** - PyTorch for GPU acceleration

## Development Tools
- **uv** - Modern Python package manager (replaces pip)
- **virtual environment** - Isolated dependencies
- **batch scripts** - Windows automation

## Project Structure
- Simplified single-module architecture (`main.py`)
- Intelligent model selection (NB-Whisper for Norwegian)
- YAML-based configuration
- Batch processing support
- Interactive CLI interface
- UV-based dependency management

## AI Model Architecture
- **Dual Model Support**: Standard Whisper + NB-Whisper
- **Automatic Language Detection**: Intelligent Norwegian detection
- **Optimized Settings**: Model-specific configurations for best quality
- **Graceful Fallback**: Automatic fallback between model types
- **GPU Acceleration**: CUDA support for faster processing
