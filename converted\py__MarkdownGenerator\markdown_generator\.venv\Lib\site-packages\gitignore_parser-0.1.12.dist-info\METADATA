Metadata-Version: 2.4
Name: gitignore_parser
Version: 0.1.12
Summary: A spec-compliant gitignore parser for Python 3.5+
Home-page: https://github.com/mherrmann/gitignore_parser
Author: <PERSON>
Author-email: micha<PERSON>+removethi<PERSON><PERSON><PERSON><PERSON><PERSON>@herrmann.io
License: MIT
Keywords: gitignore
Platform: MacOS
Platform: Windows
Platform: Debian
Platform: Fedora
Platform: CentOS
Classifier: Development Status :: 4 - Beta
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.5
Classifier: Programming Language :: Python :: 3.6
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Topic :: Software Development :: Libraries
Classifier: Topic :: Software Development :: Libraries :: Python Modules
License-File: LICENSE
Dynamic: author
Dynamic: author-email
Dynamic: classifier
Dynamic: description
Dynamic: home-page
Dynamic: keywords
Dynamic: license
Dynamic: license-file
Dynamic: platform
Dynamic: summary

A spec-compliant gitignore parser for Python 3.5+

https://github.com/mherrmann/gitignore_parser
