# Markdown Generator

Generate comprehensive Markdown documentation from file structures with rich CLI interface.

## Features

- **Interactive CLI**: Rich-based interface with progress bars and styled output
- **Flexible File Filtering**: Support for extension groups, patterns, and .gitignore integration
- **Smart Content Handling**: Automatic encoding detection and content exclusion markers
- **Customizable Output**: Configurable depth, structure preview, and file ordering
- **Modern Dependencies**: Minimal, essential packages only

## Quick Start

### Using uv (Recommended)

```bash
# Run with interactive prompts
run.bat

# Or directly with uv
uv run python src/main.py --prompt
```

### Traditional Python

```bash
python src/main.py --prompt
```

## Usage Examples

```bash
# Interactive mode (prompts for all options)
uv run python src/main.py --prompt

# Specify input/output with interactive prompts for other options
uv run python src/main.py -i /path/to/source -op /path/to/output --prompt

# Non-interactive with specific options
uv run python src/main.py -i /path/to/source -op /path/to/output -of documentation.md -e py js html
```

## Configuration Options

- **File Types**: Choose specific extensions or predefined groups (Python, Web, etc.)
- **Depth Control**: Limit directory traversal depth
- **Exclusions**: Use .gitignore patterns or custom exclusion rules
- **Structure Options**: Include/exclude empty directories, file ordering preferences
- **Output Control**: Custom filenames, cleanup options

## Dependencies

- **chardet**: Character encoding detection
- **gitignore-parser**: .gitignore file parsing
- **loguru**: Advanced logging
- **PyYAML**: YAML configuration support
- **rich**: Terminal UI framework

## Project Structure

```
markdown_generator/
├── src/
│   └── main.py          # Main application
├── pyproject.toml       # Project configuration
├── run.bat             # Universal runner script
└── README.md           # This file
```
