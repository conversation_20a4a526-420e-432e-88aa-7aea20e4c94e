# https://github.com/cline/cline/blob/main/.vscodeignore

# Default
.vscode/**
.vscode-test/**
out/**
node_modules/**
src/**
.gitignore
.yarnrc
esbuild.js
vsc-extension-quickstart.md
**/tsconfig.json
**/.eslintrc.json
**/*.map
**/*.ts
**/.vscode-test.*

# Ignore all webview-ui files except the build directory (https://github.com/microsoft/vscode-webview-ui-toolkit-samples/blob/main/frameworks/hello-world-react-cra/.vscodeignore)
webview-ui/src/**
webview-ui/public/**
webview-ui/index.html
webview-ui/README.md
webview-ui/package.json
webview-ui/package-lock.json
webview-ui/node_modules/**
**/.gitignore

# Ignore docs
docs/**

# Fix issue where codicons don't get packaged (https://github.com/microsoft/vscode-extension-samples/issues/692)
!node_modules/@vscode/codicons/dist/codicon.css
!node_modules/@vscode/codicons/dist/codicon.ttf

# Include default themes JSON files used in getTheme
!src/integrations/theme/default-themes/**

# Include icons
!assets/icons/**


# =======================================================
# -- OVERRIDES --
# =======================================================

# filenames: unsorted
**/.what-is-this.md
**/app.log.yml
**/quit.blend
**/Run History-1.5a.csv
**/Search History-1.5a.csv
**/Session-1.5a.backup.json
**/Session-1.5a.json

# dirs
**/.backups/
**/.specstory/
**/__meta__/

# types
*.sublime-workspace
*.sublime_session

# paths: files
**/*sync-conflict*.*
**/user-data/**/ui_messages.json
**/.specstory/history/.what-is-this.md
