import argparse
import fnmatch
import os
import platform
import re
import sys
from pathlib import Path

import chardet
import yaml
from gitignore_parser import parse_gitignore
from loguru import logger
from rich import box
from rich.console import Console
from rich.prompt import Confirm, Prompt
from rich.progress import (
    Progress,
    SpinnerColumn,
    TimeElapsedColumn,
    TextColumn,
    BarColumn,
    MofNCompleteColumn,
)
from rich.table import Table

class Config:

    # Configuration Constants
    # =======================================================
    DEFAULT_DEPTH = 99
    DEFAULT_EXTENSIONS = ["py"]
    DEFAULT_INCLUDE_ALL_FILES = True
    DEFAULT_PREDEFINED_EXTENSION_GROUPS = False
    DEFAULT_FULL_STRUCTURE_PREVIEW = False
    DEFAULT_CLEANUP_LOGS = True
    DEFAULT_FILES_FIRST = True

    # Directory exclusions - always exclude completely
    EXCLUDED_DIRS = [
        ".backups", ".github", ".git", ".gpt", ".ignore",
        ".old", ".cmd", ".tmp", ".venv", ".versions", ".specstory",
        "__meta__", "__pycache__", "__tmp__", "venv", "node_modules",
    ]

    # Complete exclusions (excluded from both structure and content)
    EXCLUDED_PATTERNS = [
        # Compiled/bytecode files
        "*.pyc", "*.pyo", "*.class", 
        # Database files
        "*.db", 
        # Binary/executable files
        "*.exe", "*.dll", "*.so", "*.dylib", "*.bin",
        # Log files
        "*.log", "*.log.yml",  "uv.lock",
        # Special files
        ".new_hashes.py", ".original_hashes.py",
        # Commented out but kept for reference
        # "*.bat"
    ]

    # Regex patterns for complete exclusion
    EXCLUDED_REGEX = [r".*\.tmp$"]

    # Content-only exclusions (show in structure but exclude from content)
    CONTENT_ONLY_EXCLUDED_PATTERNS = [
        # Image files
        "*.ico", "*.png", "*.jpg", "*.jpeg", "*.heic", "*.gif", "*.bmp", "*.svg",
        # Audio files
        "*.mp3", "*.wav", "*.ogg", "*.flac", "*.aac",
        # Video files
        "*.mp4", "*.avi", "*.mov", "*.mkv", "*.webm", "*.m4a",
        # Document files
        "*.pdf", "*.docx", "*.xlsx", "*.pptx",
        # Font files
        "*.ttf", "*.otf", "*.woff", "*.woff2",
        # Archive files
        "*.zip", "*.tar", "*.gz", "*.rar", "*.7z",
        # IDE files
        "*.sublime-workspace"
    ]
    
    # Regex patterns for content-only exclusion
    CONTENT_ONLY_EXCLUDED_REGEX = []

    # Default markers for visual indicators
    CONTENT_EXCLUDED_MARKER = "[-]"  # Marker for files in structure but excluded from content

    EXTENSION_GROUPS = {
        # "SublimeText": ["py", "*sublime*", "tmLanguage", "tmPreferences", "tmTheme", "stTheme"],
        "SublimeText": ["py", "sublime-commands", "sublime-keymap", "sublime-settings", "sublime-menu"],
        "Python": ["*pdm*", "env", "py", "pyi", "pyo", "toml", "jinja*"],
        "bat|py|txt": ["bat", "py", "txt"],
        "Web:React": ["ts", "tsx", "js", "json", "cjs", "css", "html", ],
        # "Web": ["css", "html", "js"],
        # "Data": ["cfg", "csv", "json", "xml"],
    }

    CODE_BLOCK_TYPES = {
        "py": "python", "json": "json", "nss": "java", "log": "text", "txt": "text",
        "md": "markdown", "html": "html", "htm": "html", "css": "css", "js": "javascript",
        "ts": "typescript", "xml": "xml", "yaml": "yaml", "yml": "yaml", "sh": "bash",
        "bat": "batch", "ini": "ini", "cfg": "ini", "java": "java", "c": "c", "cpp": "cpp",
        "h": "cpp", "hpp": "cpp", "cs": "csharp", "go": "go", "rb": "ruby", "php": "php",
        "sql": "sql", "swift": "swift", "kt": "kotlin", "rs": "rust", "r": "r", "pl": "perl",
        "lua": "lua", "scala": "scala", "vb": "vbnet",
    }

class LoggerSetup:

    # Logging
    # =======================================================
    @staticmethod
    def setup_yaml_logging(log_file: str = "app.log.yml", level: str = "INFO"):
        def yaml_sink(message):
            record = message.record

            time_str = f"{record['time'].strftime('%Y-%m-%d %H:%M:%S')}"
            level_str = f"!{record['level'].name}"
            name_str = record['name']
            funcName_str = f"*{record['function']}"
            lineno_int = record["line"]
            msg = record["message"]

            if "\n" in msg:
                lines = msg.split("\n")
                message_str = "|\n" + "\n".join(f"  {line}" for line in lines)
            else:
                # Quote message if it has special characters
                if ":" in msg:
                    message_str = f"'{msg}'"
                else:
                    message_str = msg

            yaml_lines = [
                f"- time: {time_str}",
                f"  level: {level_str}",
                f"  name: {name_str}",
                f"  funcName: {funcName_str}",
                f"  lineno: {lineno_int}",
                f"  message: {message_str}",
                ""
            ]

            with open(log_file, "a", encoding="utf-8") as f:
                f.write("\n".join(yaml_lines) + "\n")

        logger.remove()
        logger.add(yaml_sink, level=level, enqueue=True)

    @staticmethod
    def initialize_logging():
        LoggerSetup.setup_yaml_logging()

class ArgumentHandler:

    # Argument Parsing and Prompting
    # =======================================================
    def __init__(self):
        self.parser = self.parse_arguments()

    @staticmethod
    def parse_arguments():
        logger.debug("Setting up argument parser.")
        parser = argparse.ArgumentParser(description="Generate Markdown documentation from a file structure.")
        parser.add_argument('-i', '--input_path', type=str, help="Input directory path")
        parser.add_argument('-op', '--output_path', type=str, help="Output directory path")
        parser.add_argument('-of', '--output_filename', type=str, help="Output markdown filename")
        parser.add_argument('-d', '--depth', type=int, help="Max directory depth", default=Config.DEFAULT_DEPTH)
        parser.add_argument('--include_all_files', action='store_true', help="Include all file types", default=Config.DEFAULT_INCLUDE_ALL_FILES)
        parser.add_argument('-e', '--extensions', nargs='+', help="File extensions/groups", default=Config.DEFAULT_EXTENSIONS)
        parser.add_argument('--use-gitignore', action='store_true', help="Use .gitignore for exclusions", default=False)
        parser.add_argument('-edp', '--exclude_dir_patterns', nargs='+', help="Excluded directory patterns")
        parser.add_argument('-efp', '--exclude_file_patterns', nargs='+', help="Excluded file patterns")
        parser.add_argument('--show_all_files_in_filestructure', action='store_true', default=Config.DEFAULT_FULL_STRUCTURE_PREVIEW, help="Show all files in structure")
        parser.add_argument('--include_empty_dirs', action='store_true', help="Include empty directories")
        parser.add_argument('--files_first', action='store_true', default=Config.DEFAULT_FILES_FIRST, help="List files before directories in the output")
        parser.add_argument('--prompt', action='store_true', help="Prompt for input values")
        parser.add_argument('--log_to_current_dir', action='store_true', help="Log to current dir")

        # Arguments for Log Cleanup
        cleanup_logs_group = parser.add_mutually_exclusive_group()
        cleanup_logs_group.add_argument('--cleanup-logs', dest='cleanup_logs', action='store_true', help="Clean up log files after successful execution")
        cleanup_logs_group.add_argument('--no-cleanup-logs', dest='cleanup_logs', action='store_false', help="Do not clean up log files after successful execution")
        parser.set_defaults(cleanup_logs=Config.DEFAULT_CLEANUP_LOGS)

        logger.debug("Argument parser setup complete.")
        return parser

    def get_arguments(self):
        return self.parser.parse_args()

    def get_user_inputs(self, args):
        """Get user inputs following the pattern from converted projects"""
        logger.debug("Getting user inputs.")
        console = Console()

        def print_section(title):
            console.print(f"\n[bold blue] --- {title} ---[/bold blue]", highlight=False)

        # Required arguments - prompt if missing or if --prompt is used
        if args.prompt or not args.input_path:
            args.input_path = Prompt.ask("Input directory path", default=args.input_path or os.getcwd())

        if args.prompt or not args.output_path:
            args.output_path = Prompt.ask("Output directory path", default=args.output_path or os.getcwd())

        if args.prompt or not args.output_filename:
            args.output_filename = Prompt.ask("Output filename (.md)", default=args.output_filename or 'py__MarkdownGenerator.md')

        # If --prompt is used, ask for all configuration options
        if args.prompt:
            print_section("File Types")
            args.include_all_files = Confirm.ask("Include all file types?", default=args.include_all_files)

            if not args.include_all_files:
                if Confirm.ask("Use predefined extension group?", default=Config.DEFAULT_PREDEFINED_EXTENSION_GROUPS):
                    group_names = list(Config.EXTENSION_GROUPS.keys())
                    table = Table(header_style="bold magenta", box=box.SIMPLE)
                    table.add_column("No.", style="bold cyan", justify="right")
                    table.add_column("Group Name", style="bold cyan")
                    table.add_column("Extensions", style="magenta")
                    for i, group in enumerate(group_names, 1):
                        exts = ', '.join(Config.EXTENSION_GROUPS[group])
                        table.add_row(str(i), group, exts)
                    console.print(table)
                    choice = Prompt.ask("Choose group by number:", choices=[str(i) for i in range(1, len(group_names) + 1)])
                    selected_group = group_names[int(choice) - 1]
                    args.extensions = Config.EXTENSION_GROUPS[selected_group]
                else:
                    exts_input = Prompt.ask("File extensions (space-separated):", default=' '.join(args.extensions or Config.DEFAULT_EXTENSIONS))
                    args.extensions = exts_input.split()

            print_section("Optional Settings")
            depth_input = Prompt.ask("Max depth:", default=str(args.depth))
            args.depth = int(depth_input) if depth_input.isdigit() else Config.DEFAULT_DEPTH

            if not args.include_all_files:
                args.show_all_files_in_filestructure = Confirm.ask("Include unmatched files in structure?", default=args.show_all_files_in_filestructure)

            args.include_empty_dirs = Confirm.ask("Include empty directories?", default=args.include_empty_dirs)
            args.files_first = Confirm.ask("List files before directories?", default=args.files_first)

            print_section("Exclusions")
            args.use_gitignore = Confirm.ask("Use .gitignore for exclusions?", default=args.use_gitignore)
            excl_dir = Prompt.ask("Exclude directory patterns (space-separated):", default=' '.join(args.exclude_dir_patterns or []))
            args.exclude_dir_patterns = excl_dir.split() if excl_dir else []
            excl_file = Prompt.ask("Exclude file patterns (space-separated):", default=' '.join(args.exclude_file_patterns or []))
            args.exclude_file_patterns = excl_file.split() if excl_file else []

            print_section("Cleanup")
            args.cleanup_logs = Confirm.ask("Clean up log files after successful execution?", default=args.cleanup_logs)

        # Ensure defaults for any unset values
        args.input_path = args.input_path or os.getcwd()
        args.output_path = args.output_path or os.getcwd()
        args.output_filename = args.output_filename or 'py__MarkdownGenerator.md'
        args.extensions = args.extensions or Config.DEFAULT_EXTENSIONS
        args.exclude_dir_patterns = args.exclude_dir_patterns or []
        args.exclude_file_patterns = args.exclude_file_patterns or []

        logger.debug("User input collection complete.")
        return args

class FileExcluder:

    # File Exclusion Logic
    # =======================================================
    def __init__(self, root_dir: Path, exclude_dir_patterns=None, exclude_file_patterns=None,
                 exclude_regex=None, use_gitignore=False):
        self.root_dir = root_dir
        self.exclude_dir_patterns = exclude_dir_patterns or []
        self.exclude_file_patterns = exclude_file_patterns or []
        self.exclude_regex = exclude_regex or []
        self.gitignore_patterns = self.load_gitignore_patterns() if use_gitignore else []
        self.exclusion_counters = {"dirs": 0, "patterns": 0, "regex": 0, "gitignore": 0, "content_only": 0}
        # Track files that should be excluded from content but shown in structure
        self.content_only_excluded_files = set()

    def load_gitignore_patterns(self):
        gitignore_path = self.root_dir / '.gitignore'
        if not gitignore_path.exists():
            logger.debug(f"No .gitignore found at {gitignore_path}")
            return []

        with open(gitignore_path, 'r') as file:
            lines = file.readlines()

        patterns = []
        for line in lines:
            stripped = line.strip()
            if stripped and not stripped.startswith('#'):
                if stripped.endswith('/'):
                    stripped = stripped.rstrip('/') + '/**'
                elif stripped.startswith('/'):
                    stripped = stripped.lstrip('/')
                patterns.append(stripped)
        logger.debug(f".gitignore patterns: {patterns}")
        return patterns

    def is_content_excluded(self, path: Path) -> bool:
        """
        Check if a file should be excluded from content but shown in structure.
        Returns True if the file should be excluded from content only.
        """
        if path.is_dir():
            return False  # Directories are never content-excluded
            
        relative_filepath = path.relative_to(self.root_dir).as_posix()
        
        # Check if file matches any content-only exclusion patterns
        for pattern in Config.CONTENT_ONLY_EXCLUDED_PATTERNS:
            if fnmatch.fnmatch(path.name, pattern):
                self.exclusion_counters['content_only'] += 1
                self.content_only_excluded_files.add(path)
                return True
                
        # Check content-only excluded regex patterns
        for pattern in Config.CONTENT_ONLY_EXCLUDED_REGEX:
            if re.match(pattern, relative_filepath):
                self.exclusion_counters['content_only'] += 1
                self.content_only_excluded_files.add(path)
                return True
                
        return False
    
    def is_excluded(self, path: Path) -> bool:
        """
        Check if a path should be completely excluded from both structure and content.
        Returns True if the path should be completely excluded.
        """
        relative_filepath = path.relative_to(self.root_dir).as_posix()

        # Check if any part of the path is in EXCLUDED_DIRS
        for part in path.parts:
            if part in Config.EXCLUDED_DIRS + self.exclude_dir_patterns:
                if path.is_dir():
                    self.exclusion_counters['dirs'] += 1
                else:
                    self.exclusion_counters['patterns'] += 1
                return True

        # Check excluded regex patterns
        for pattern in Config.EXCLUDED_REGEX + self.exclude_regex:
            if re.match(pattern, relative_filepath):
                self.exclusion_counters['regex'] += 1
                return True

        # Check gitignore patterns
        for pattern in self.gitignore_patterns:
            if fnmatch.fnmatch(relative_filepath, pattern):
                self.exclusion_counters['gitignore'] += 1
                return True

        # Check excluded file patterns
        if path.is_file():
            for pattern in Config.EXCLUDED_PATTERNS + self.exclude_file_patterns:
                if fnmatch.fnmatch(path.name, pattern):
                    self.exclusion_counters['patterns'] += 1
                    return True

        return False

class MarkdownGenerator:

    # Markdown Generation Logic
    # =======================================================
    def __init__(self, root_dir: Path, output_file: Path, max_depth=None, extensions=None,
                 include_empty_dirs=False, exclude_dir_patterns=None, exclude_file_patterns=None,
                 include_all_files=False, show_all_files_in_filestructure=False, use_gitignore=False,
                 files_first=False):
        self.root_dir = root_dir
        self.output_file = output_file
        self.max_depth = max_depth
        self.extensions = self.resolve_extensions(extensions)
        self.include_empty_dirs = include_empty_dirs
        self.exclude_dir_patterns = exclude_dir_patterns
        self.exclude_file_patterns = exclude_file_patterns
        self.include_all_files = include_all_files
        self.show_all_files_in_filestructure = show_all_files_in_filestructure
        self.use_gitignore = use_gitignore
        self.files_first = files_first
        self.excluder = FileExcluder(
            root_dir=self.root_dir,
            exclude_dir_patterns=self.exclude_dir_patterns,
            exclude_file_patterns=self.exclude_file_patterns,
            use_gitignore=self.use_gitignore
        )
        self.console = Console()

    def resolve_extensions(self, extensions_list):
        logger.debug(f"Resolving extensions: {extensions_list}")
        resolved = []
        for item in extensions_list:
            if item in Config.EXTENSION_GROUPS:
                resolved.extend(Config.EXTENSION_GROUPS[item])
                logger.debug(f"Group '{item}' -> {Config.EXTENSION_GROUPS[item]}")
            else:
                resolved.append(item)
                logger.debug(f"Extension '{item}' added")
        logger.debug(f"Final extensions: {resolved}")
        return resolved

    def get_code_block_type(self, ext):
        return Config.CODE_BLOCK_TYPES.get(ext, ext)

    def generate_markdown_for_file(self, file_path: Path):
        relative_filepath = file_path.relative_to(self.root_dir)
        ext = file_path.suffix[1:]
        encodings = ['utf-8', 'utf-8-sig', 'latin-1', 'cp1252', 'utf-16', 'utf-16-le', 'utf-16-be']
        content = None

        try:
            with open(file_path, 'rb') as f:
                raw = f.read(10000)
            detected = chardet.detect(raw)['encoding']
            if detected:
                encodings.insert(0, detected)
        except Exception as e:
            logger.error(f"Encoding detection error for {file_path}: {e}")

        for enc in encodings:
            try:
                with open(file_path, 'r', encoding=enc) as f:
                    content = f.read()
                break
            except UnicodeDecodeError:
                continue
            except Exception as e:
                logger.error(f"Error reading {file_path} with {enc}: {e}")

        if content is None:
            return f"#### `{relative_filepath}`\n\nError: Unable to read file.\n\n"

        # Current file header
        file_header = f"\n\n---\n\n#### `{relative_filepath}`\n\n"

        # Determine block type based on file extension
        block_type = self.get_code_block_type(ext)
        block_prefix = f"```{block_type}\n" if block_type else ""
        block_suffix = "\n```" if block_type else ""

        # Indent content by 4 spaces and combine elements
        lines = content.splitlines()
        indented_content = '\n'.join(f"    {line}" for line in lines)
        content_entry = f"{file_header}{block_prefix}{indented_content}{block_suffix}\n\n"

        return content_entry

    def build_tree(self, paths):
        tree = {}
        for p in paths:
            parts = p.relative_to(self.root_dir).parts
            node = tree
            for part in parts[:-1]:
                node = node.setdefault(part, {})
            node.setdefault(parts[-1], {})
        return tree

    def is_content_excluded_path(self, path):
        """Check if a path is in the content-excluded files set"""
        return path in self.excluder.content_only_excluded_files

    def print_tree(self, node, prefix="", current_path=None):
        lines = []
        keys = list(node.keys())
        
        if current_path is None:
            current_path = self.root_dir

        # Separate files and directories
        files = [k for k in keys if not node[k]]  # Empty dict means it's a file
        dirs = [k for k in keys if node[k]]       # Non-empty dict means it's a directory

        # Sort files and directories separately
        files.sort()
        dirs.sort()

        # Combine files and directories based on files_first parameter
        sorted_keys = files + dirs if self.files_first else dirs + files

        for i, key in enumerate(sorted_keys):
            connector = "└──" if i == len(sorted_keys)-1 else "├──"
            
            # For files, check if content-excluded
            if key in files:
                path = current_path / key
                if self.excluder.is_content_excluded(path):
                    lines.append(f"{prefix}{connector} {key} {Config.CONTENT_EXCLUDED_MARKER}")
                else:
                    lines.append(f"{prefix}{connector} {key}")
            else:
                # For directories, no exclusion marker
                lines.append(f"{prefix}{connector} {key}")
                
            sub_node = node[key]
            if sub_node:  # If it's a directory
                extension = "    " if i == len(sorted_keys)-1 else "│   "
                next_path = current_path / key
                lines.extend(self.print_tree(sub_node, prefix=prefix+extension, current_path=next_path))
        return lines

    def generate_markdown(self):
        try:
            gitignore_patterns = self.excluder.gitignore_patterns
            if gitignore_patterns:
                self.exclude_dir_patterns = (self.exclude_dir_patterns or []) + gitignore_patterns
                self.exclude_file_patterns = (self.exclude_file_patterns or []) + gitignore_patterns

            logger.debug(f"Generating markdown for {self.root_dir} -> {self.output_file}")
            logger.debug(f"Extensions: {self.extensions}, Include all: {self.include_all_files}, Show all in structure: {self.show_all_files_in_filestructure}")

            structure_patterns = ["*"] if self.include_all_files or self.show_all_files_in_filestructure else [f"*.{ext}" for ext in self.extensions]
            content_patterns = ["*"] if self.include_all_files else [f"*.{ext}" for ext in self.extensions]

            markdown_content = f"# Dir `{self.root_dir.stem}`\n\n"
            # Always show the marker explanation if content-excluded files can appear in the structure
            # markdown_content += f"*Files marked `{Config.CONTENT_EXCLUDED_MARKER}` are shown in structure but not included in content.*\n\n"

            excluded = []
            processed = []
            counters = self.excluder.exclusion_counters

            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                BarColumn(),
                MofNCompleteColumn(),
                TimeElapsedColumn(),
                console=self.console,
            ) as progress:
                paths = sorted(self.root_dir.rglob("*"))
                total = len(paths)
                logger.debug(f"Found {total} paths.")
                task1 = progress.add_task("[cyan]Gathering file structure...", total=total)

                included_paths = []
                for path in paths:
                    if self.excluder.is_excluded(path):
                        excluded.append(path)
                    else:
                        if ((self.max_depth is None or len(path.relative_to(self.root_dir).parts) <= self.max_depth)
                            and (path.is_dir() or any(fnmatch.fnmatch(path.name, pat) for pat in structure_patterns))
                            and (self.include_empty_dirs or not path.is_dir() or
                                 any(
                                     not self.excluder.is_excluded(f) and f.is_file() and any(fnmatch.fnmatch(f.name, p) for p in structure_patterns)
                                     for f in path.rglob("*")
                                 ))
                           ):
                            included_paths.append(path)
                    progress.update(task1, advance=1)

                tree = self.build_tree(included_paths)
                file_structure_lines = self.print_tree(tree)
                file_structure_str = "### File Structure\n\n```\n" + "\n".join(file_structure_lines) + "\n```\n"
                markdown_content += file_structure_str

                progress.update(task1, completed=total)
                logger.debug(f"Excluded {len(excluded)} paths: {counters}")

                # Files to process for content (excluding content-only excluded files)
                files = [p for p in paths if p not in excluded and p.is_file()
                         and not self.excluder.is_content_excluded(p)
                         and (self.max_depth is None or len(p.relative_to(self.root_dir).parts) <= self.max_depth)
                         and any(fnmatch.fnmatch(p.name, pat) for pat in content_patterns)]

                # Sort files based on files_first parameter
                def sort_key(path):
                    parts = path.relative_to(self.root_dir).parts
                    if self.files_first:
                        # For files-first, we want files in the current directory to come before subdirectories
                        return (len(parts), 0 if len(parts) == 1 else 1) + parts
                    else:
                        # For directories-first, we want files in subdirectories to be grouped with their directories
                        return parts

                files.sort(key=sort_key)
                logger.debug(f"{len(files)} files to process.")
                task2 = progress.add_task("[cyan]Processing files for content...", total=len(files))

                for idx, file in enumerate(files, 1):
                    try:
                        content = self.generate_markdown_for_file(file)
                        markdown_content += f"{content}"
                        processed.append(file)
                    except Exception as e:
                        logger.error(f"Failed on {file}: {e}")
                    finally:
                        progress.update(task2, advance=1)

                logger.debug(f"Processed {len(processed)} files.")

            # Removes consecutive blank lines (preserving at most one blank line between text lines).
            markdown_content_clean = re.sub(r"\n{2,}", "\n\n", markdown_content)
            self.output_file.write_text(markdown_content_clean, encoding="utf-8")
            logger.info(f"Markdown generated at {self.output_file}")

        except Exception as e:
            logger.error(f"Markdown generation failed: {e}")
            raise

class MarkdownGeneratorApp:

    # Main Application Logic
    # =======================================================
    def __init__(self):
        LoggerSetup.initialize_logging()
        self.arg_handler = ArgumentHandler()

    @staticmethod
    def ensure_md_extension(filename):
        return f"{filename}.md" if not filename.endswith('.md') else filename

    @staticmethod
    def ensure_directory_exists(directory: Path):
        directory.mkdir(parents=True, exist_ok=True)
        logger.info(f"Created output directory: {directory}")

    @staticmethod
    def clear_console():
        os.system("cls" if platform.system() == "Windows" else "clear")

    @staticmethod
    def display_summary(console, args):
        table = Table(title="Configuration Summary", show_header=True, header_style="bold magenta", box=box.ASCII)
        table.add_column("Parameter", style="dim", width=30)
        table.add_column("Value", style="bold cyan")

        summary_data = [
            ("Input directory path", str(args.input_path)),
            ("Output markdown file path", str(args.output_path)),
            ("Output filename", str(args.output_filename)),
            ("Maximum directory depth", str(args.depth)),
            ("Include all files", "Yes" if args.include_all_files else "No"),
            ("Show all files in filestructure", "Yes" if args.show_all_files_in_filestructure else "No"),
            ("Include empty directories", "Yes" if args.include_empty_dirs else "No"),
            ("Files before directories", "Yes" if args.files_first else "No"),
            ("Excluded directory patterns", ', '.join(args.exclude_dir_patterns) if args.exclude_dir_patterns else "None"),
            ("Excluded file patterns", ', '.join(args.exclude_file_patterns) if args.exclude_file_patterns else "None"),
            ("Use .gitignore for exclusions", "Yes" if args.use_gitignore else "No"),
            ("Cleanup log files after success", "Yes" if args.cleanup_logs else "No"),
        ]

        if not args.include_all_files:
            summary_data.insert(6, ("File extensions", ', '.join(args.extensions)))

        for param, value in summary_data:
            table.add_row(param, value)

        console.print(table)

    def run(self):
        logger.debug("Main started.")
        args = self.arg_handler.get_arguments()
        logger.debug(f"Arguments: {args}")

        # Get user inputs using the new pattern
        args = self.arg_handler.get_user_inputs(args)
        logger.debug(f"Post-input arguments: {args}")

        if not args.input_path or not args.output_path:
            Console().print("[bold red]Error: Input and output directories required.[/bold red]")
            logger.error("Input and output directories required.")
            return

        input_path = Path(args.input_path)
        output_path = Path(args.output_path)
        output_filename = self.ensure_md_extension(args.output_filename) if args.output_filename else "py__MarkdownGenerator.md"
        full_output = output_path / output_filename

        logger.debug(f"Input: {input_path}, Output: {output_path}, Filename: {output_filename}")

        self.ensure_directory_exists(output_path)
        self.display_summary(Console(), args)

        try:
            generator = MarkdownGenerator(
                root_dir=input_path,
                output_file=full_output,
                max_depth=args.depth,
                extensions=args.extensions,
                include_empty_dirs=args.include_empty_dirs,
                exclude_dir_patterns=args.exclude_dir_patterns,
                exclude_file_patterns=args.exclude_file_patterns,
                include_all_files=args.include_all_files,
                show_all_files_in_filestructure=args.show_all_files_in_filestructure,
                use_gitignore=args.use_gitignore,
                files_first=args.files_first,
            )
            generator.generate_markdown()
            Console().print(f"\nMarkdown generated at [bold cyan]{full_output}[/bold cyan]\n")

            # Cleanup log file if specified and successful execution
            if args.cleanup_logs:
                self.cleanup_logs()

        except Exception as e:
            logger.error(f"Generation failed: {e}")
            Console().print(f"\n[bold red]Error:[/bold red] {e}\n")
            raise

    def cleanup_logs(self):
        """Clean up log files after successful execution"""
        logger.remove()
        log_file = Path("app.log.yml")
        if log_file.exists():
            try:
                log_file.unlink()
                Console().print(f"Log file [bold green]{log_file}[/bold green] has been cleaned up.\n")
            except Exception as e:
                Console().print(f"[bold red]Failed to delete log file {log_file}: {e}[/bold red]\n")
        else:
            Console().print(f"No log file found to clean up at {log_file}\n")

def wait_for_user_exit():
    """Wait for user to press any key before exiting"""
    console = Console()
    console.print(f"\n[bold cyan]Press any key to exit...[/bold cyan]")
    try:
        input()
    except KeyboardInterrupt:
        pass

def main():
    try:
        app = MarkdownGeneratorApp()
        app.run()
    except Exception as e:
        console = Console()
        console.print(f"\n[bold red]Error:[/bold red] {e}")
        logger.exception(f"Main execution error: {e}")
    finally:
        console = Console()
        console.print("\n[bold green]Finished processing.[/bold green]")
        wait_for_user_exit()

if __name__ == "__main__":
    main()
